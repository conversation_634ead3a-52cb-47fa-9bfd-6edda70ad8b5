"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-table.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingTable: function() { return /* binding */ UnifiedTrainingTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst getRowStatus = (rowData)=>{\n    if (rowData.status.isOverdue) {\n        return \"overdue\";\n    }\n    if (rowData.status.dueWithinSevenDays) {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\n// Status-based color classes for training titles\nconst getStatusColorClasses = (training)=>{\n    if (training.status.isOverdue) {\n        return \"text-destructive/80 hover:text-destructive\";\n    }\n    if (training.status.dueWithinSevenDays) {\n        return \"text-warning/80 hover:text-warning\";\n    }\n    return \"hover:text-curious-blue-400\";\n};\nconst UnifiedMobileTrainingCard = (param)=>{\n    let { data } = param;\n    var _data_trainingType, _data_originalData_trainingTypes, _data_originalData, _data_vessel, _data_originalData1, _data_originalData2;\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const isCompleted = data.category === \"completed\";\n    const isOverdue = data.category === \"overdue\";\n    const members = data.members || [];\n    const trainingTitle = ((_data_trainingType = data.trainingType) === null || _data_trainingType === void 0 ? void 0 : _data_trainingType.title) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-3 tablet-md:border-none border-b border-border py-3 small:pe-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap justify-between items-center\",\n                children: [\n                    isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/crew-training/info?id=\".concat(data.id),\n                        className: \"font-semibold text-base hover:text-primary\",\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-semibold text-base\", getStatusColorClasses(data)),\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 21\n                    }, undefined),\n                    !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                className: \"text-sm m-0 text-muted-foreground\",\n                                children: \"Status:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-sm font-medium px-2 py-1 rounded-md\", data.status.isOverdue ? \"bg-destructive/10 text-destructive\" : data.status.dueWithinSevenDays ? \"bg-warning/10 text-warning\" : \"bg-muted/50 text-muted-foreground\"),\n                                children: data.status.label || data.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 85,\n                columnNumber: 13\n            }, undefined),\n            !bp[\"tablet-md\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-[7px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Training/drill:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm\",\n                        children: isCompleted ? ((_data_originalData = data.originalData) === null || _data_originalData === void 0 ? void 0 : (_data_originalData_trainingTypes = _data_originalData.trainingTypes) === null || _data_originalData_trainingTypes === void 0 ? void 0 : _data_originalData_trainingTypes.nodes) ? data.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : trainingTitle : trainingTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 125,\n                columnNumber: 17\n            }, undefined),\n            !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Location:\" : \"Vessel:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_data_vessel = data.vessel) === null || _data_vessel === void 0 ? void 0 : _data_vessel.title) || ((_data_originalData1 = data.originalData) === null || _data_originalData1 === void 0 ? void 0 : _data_originalData1.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: data.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 143,\n                columnNumber: 17\n            }, undefined),\n            !bp.laptop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                position: \"left\",\n                className: \"text-sm text-muted-foreground\",\n                label: isCompleted ? \"Team:\" : \"Crew Members:\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 flex-wrap\",\n                    children: [\n                        members.slice(0, bp[\"tablet-md\"] ? 8 : 6).map((member)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: !isCompleted && isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 33\n                            }, undefined);\n                        }),\n                        members.length > (bp[\"tablet-md\"] ? 8 : 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                                    className: \"w-fit\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-fit h-8\",\n                                        children: [\n                                            \"+\",\n                                            members.length - (bp[\"tablet-md\"] ? 8 : 6),\n                                            \" \",\n                                            \"more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                                    className: \"w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 max-h-64 overflow-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: members.slice(bp[\"tablet-md\"] ? 8 : 6).map((remainingMember)=>/*#__PURE__*/ {\n                                                var _remainingMember_firstName, _remainingMember_surname;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                            size: \"xs\",\n                                                            variant: \"secondary\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                className: \"text-xs\",\n                                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(remainingMember.firstName, remainingMember.surname)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 61\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 57\n                                                        }, undefined),\n                                                        \"\".concat((_remainingMember_firstName = remainingMember.firstName) !== null && _remainingMember_firstName !== void 0 ? _remainingMember_firstName : \"\", \" \").concat((_remainingMember_surname = remainingMember.surname) !== null && _remainingMember_surname !== void 0 ? _remainingMember_surname : \"\")\n                                                    ]\n                                                }, remainingMember.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 53\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 163,\n                columnNumber: 17\n            }, undefined),\n            !bp.landscape && isCompleted && ((_data_originalData2 = data.originalData) === null || _data_originalData2 === void 0 ? void 0 : _data_originalData2.trainer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center pt-2 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Trainer:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                size: \"sm\",\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                    className: \"text-sm\",\n                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(data.originalData.trainer.firstName, data.originalData.trainer.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    data.originalData.trainer.firstName,\n                                    \" \",\n                                    data.originalData.trainer.surname\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 240,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-md\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n                        className: \"my-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Category: \",\n                                    data.category\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 25\n                            }, undefined),\n                            data.dueDate && (()=>{\n                                const today = new Date();\n                                const dueDate = new Date(data.dueDate);\n                                const daysDifference = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: daysDifference > 0 ? \"Due in \".concat(daysDifference, \" days\") : daysDifference === 0 ? \"Due today\" : \"\".concat(Math.abs(daysDifference), \" days overdue\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 37\n                                }, undefined);\n                            })()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 83,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedMobileTrainingCard, \"ZxSHrfPd9jdclp97BkZrjvTJck4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = UnifiedMobileTrainingCard;\nconst UnifiedTrainingTable = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], unifiedData: preFilteredData, getVesselWithIcon, includeCompleted = true, memberId, isVesselView = false, showToolbar = false, pageSize } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery)(\"(min-width: 720px)\");\n    // Use pre-filtered data if available, otherwise merge and sort data\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (preFilteredData && Array.isArray(preFilteredData)) {\n            return preFilteredData;\n        }\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        preFilteredData,\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Determine if we have mixed data types or primarily one type\n    const hasOverdueOrUpcoming = unifiedData.some((item)=>item.category === \"overdue\" || item.category === \"upcoming\");\n    const hasCompleted = unifiedData.some((item)=>item.category === \"completed\");\n    // Create unified column structure for all training data types\n    const getUnifiedColumns = ()=>{\n        return [\n            // Mobile column - shows training card on mobile, adapts header based on data\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                        data: training\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 28\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    // Sort by category priority first, then by date\n                    const trainingA = rowA.original;\n                    const trainingB = rowB.original;\n                    const priorityA = trainingA.category === \"overdue\" ? 1 : trainingA.category === \"upcoming\" ? 2 : 3;\n                    const priorityB = trainingB.category === \"overdue\" ? 1 : trainingB.category === \"upcoming\" ? 2 : 3;\n                    if (priorityA !== priorityB) {\n                        return priorityA - priorityB;\n                    }\n                    const dateA = new Date(trainingA.dueDate).getTime();\n                    const dateB = new Date(trainingB.dueDate).getTime();\n                    return trainingA.category === \"completed\" ? dateB - dateA : dateA - dateB;\n                }\n            },\n            // Training Type column - shows training types for all data types\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training/drill\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_trainingTypes, _training_originalData, _training_trainingType, _training_trainingType1;\n                    const training = row.original;\n                    const isCompleted = training.category === \"completed\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                        children: isCompleted ? ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_trainingTypes = _training_originalData.trainingTypes) === null || _training_originalData_trainingTypes === void 0 ? void 0 : _training_originalData_trainingTypes.nodes) ? training.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\" : ((_training_trainingType1 = training.trainingType) === null || _training_trainingType1 === void 0 ? void 0 : _training_trainingType1.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainingTypes_nodes_, _rowA_original_originalData_trainingTypes_nodes, _rowA_original_originalData_trainingTypes, _rowA_original_originalData, _rowA_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_originalData_trainingTypes_nodes_, _rowB_original_originalData_trainingTypes_nodes, _rowB_original_originalData_trainingTypes, _rowB_original_originalData, _rowB_original, _rowB_original_trainingType, _rowB_original1;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes = _rowA_original_originalData.trainingTypes) === null || _rowA_original_originalData_trainingTypes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes = _rowA_original_originalData_trainingTypes.nodes) === null || _rowA_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes_ = _rowA_original_originalData_trainingTypes_nodes[0]) === null || _rowA_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_originalData_trainingTypes_nodes_.title) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes = _rowB_original_originalData.trainingTypes) === null || _rowB_original_originalData_trainingTypes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes = _rowB_original_originalData_trainingTypes.nodes) === null || _rowB_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes_ = _rowB_original_originalData_trainingTypes_nodes[0]) === null || _rowB_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_originalData_trainingTypes_nodes_.title) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Vessel column - shows vessel information for all data types\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: isVesselView ? \"\" : \"Where\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel, _training_originalData;\n                    const training = row.original;\n                    if (isVesselView) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 32\n                        }, undefined);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Crew column - shows crew members for all data types\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Who\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_members, _training_originalData, _training_status;\n                    const training = row.original;\n                    const members = ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_members = _training_originalData.members) === null || _training_originalData_members === void 0 ? void 0 : _training_originalData_members.nodes) || training.members || [];\n                    return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: members.map((member, index)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: training.status.isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id || index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 33\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"!rounded-full size-10 flex items-center justify-center text-sm font-medium\", (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.class),\n                        children: members.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_members, _rowA_original_originalData, _rowA_original, _rowA_original1, _rowB_original_originalData_members, _rowB_original_originalData, _rowB_original, _rowB_original1, _membersA_, _membersA_1, _membersB_, _membersB_1;\n                    const membersA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_members = _rowA_original_originalData.members) === null || _rowA_original_originalData_members === void 0 ? void 0 : _rowA_original_originalData_members.nodes) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.members) || [];\n                    const membersB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_members = _rowB_original_originalData.members) === null || _rowB_original_originalData_members === void 0 ? void 0 : _rowB_original_originalData_members.nodes) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.members) || [];\n                    var _membersA__firstName, _membersA__surname;\n                    const valueA = \"\".concat((_membersA__firstName = membersA === null || membersA === void 0 ? void 0 : (_membersA_ = membersA[0]) === null || _membersA_ === void 0 ? void 0 : _membersA_.firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA === null || membersA === void 0 ? void 0 : (_membersA_1 = membersA[0]) === null || _membersA_1 === void 0 ? void 0 : _membersA_1.surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") || \"\";\n                    var _membersB__firstName, _membersB__surname;\n                    const valueB = \"\".concat((_membersB__firstName = membersB === null || membersB === void 0 ? void 0 : (_membersB_ = membersB[0]) === null || _membersB_ === void 0 ? void 0 : _membersB_.firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB === null || membersB === void 0 ? void 0 : (_membersB_1 = membersB[0]) === null || _membersB_1 === void 0 ? void 0 : _membersB_1.surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Trainer column - shows trainer for completed training, dash for others\n            {\n                accessorKey: \"trainer\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Trainer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData;\n                    const training = row.original;\n                    const trainer = (_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainer;\n                    if (!trainer || training.category !== \"completed\") {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground\",\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    var _trainer_surname;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-nowrap\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                    children: [\n                                        trainer.firstName,\n                                        \" \",\n                                        (_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainer, _rowA_original_originalData, _rowA_original, _rowA_original_originalData_trainer1, _rowA_original_originalData1, _rowA_original1, _rowB_original_originalData_trainer, _rowB_original_originalData, _rowB_original, _rowB_original_originalData_trainer1, _rowB_original_originalData1, _rowB_original1;\n                    const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainer = _rowA_original_originalData.trainer) === null || _rowA_original_originalData_trainer === void 0 ? void 0 : _rowA_original_originalData_trainer.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_originalData1 = _rowA_original1.originalData) === null || _rowA_original_originalData1 === void 0 ? void 0 : (_rowA_original_originalData_trainer1 = _rowA_original_originalData1.trainer) === null || _rowA_original_originalData_trainer1 === void 0 ? void 0 : _rowA_original_originalData_trainer1.surname) || \"\") || \"\";\n                    const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainer = _rowB_original_originalData.trainer) === null || _rowB_original_originalData_trainer === void 0 ? void 0 : _rowB_original_originalData_trainer.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_originalData1 = _rowB_original1.originalData) === null || _rowB_original_originalData1 === void 0 ? void 0 : (_rowB_original_originalData_trainer1 = _rowB_original_originalData1.trainer) === null || _rowB_original_originalData_trainer1 === void 0 ? void 0 : _rowB_original_originalData_trainer1.surname) || \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ];\n    };\n    // Create table columns\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)(getUnifiedColumns()), [\n        hasOverdueOrUpcoming,\n        hasCompleted,\n        isVesselView,\n        isWide\n    ]);\n    if (!(unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: \"No training data available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n            lineNumber: 604,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.FilteredTable, {\n        columns: columns,\n        data: unifiedData,\n        showToolbar: showToolbar,\n        rowStatus: getRowStatus,\n        pageSize: pageSize || 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 611,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(UnifiedTrainingTable, \"x7VOLZGRwr6UzHBlctBEB/GNMsI=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery\n    ];\n});\n_c1 = UnifiedTrainingTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingTable);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnifiedMobileTrainingCard\");\n$RefreshReg$(_c1, \"UnifiedTrainingTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\n"));

/***/ })

});