"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // const [filter, setFilter] = useState({})\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isVesselView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overdueSwitcher, setOverdueSwitcher] = (0,nuqs__WEBPACK_IMPORTED_MODULE_18__.useQueryState)(\"overdue\");\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_19__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    // Create a boolean state wrapper for the useTrainingFilters hook\n    const [overdueBoolean, setOverdueBoolean] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Sync the boolean state with the query state\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setOverdueBoolean(overdueSwitcher === \"true\");\n    }, [\n        overdueSwitcher\n    ]);\n    // Create a wrapper function that converts boolean to string for the query state\n    const toggleOverdueWrapper = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        if (typeof value === \"function\") {\n            setOverdueBoolean((prev)=>{\n                const newValue = value(prev);\n                setOverdueSwitcher(newValue ? \"true\" : \"false\");\n                return newValue;\n            });\n        } else {\n            setOverdueBoolean(value);\n            setOverdueSwitcher(value ? \"true\" : \"false\");\n        }\n    }, [\n        setOverdueSwitcher\n    ]);\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            if (transformedData) {\n                setTrainingList(transformedData);\n                setVesselIdOptions(vesselIDs);\n                setTrainingTypeIdOptions(trainingTypeIDs);\n                setTrainerIdOptions(trainerIDs);\n                setCrewIdOptions(memberIDs);\n            }\n            setPageInfo(response.readTrainingSessions.pageInfo);\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadTrainingSessionDues(filter);\n        loadTrainingList(newPage, filter);\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: toggleOverdueWrapper\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: overdueBoolean,\n            setOverdue: toggleOverdueWrapper\n        };\n    }, [\n        handleFilterChange,\n        overdueBoolean,\n        toggleOverdueWrapper\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match OverdueTrainingList structure\n    const transformTrainingListToOverdueFormat = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                }\n            };\n        });\n    };\n    // Combined loading state for unified view\n    const isUnifiedDataLoading = excludeFilters.includes(\"overdueToggle\") ? trainingListLoading || trainingSessionDuesLoading : false;\n    // Create unified dataset when overdueToggle is excluded\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            const transformedCompletedTraining = transformTrainingListToOverdueFormat(trainingList || []);\n            return [\n                ...trainingSessionDues || [],\n                ...transformedCompletedTraining\n            ];\n        }\n        return trainingSessionDues || [];\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 403,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 405,\n            columnNumber: 13\n        }, undefined);\n    }\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Completed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: training,\n                    memberId: memberId,\n                    type: \"completed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.date) || 0).getTime();\n                const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.date) || 0).getTime();\n                return dateB - dateA;\n            }\n        },\n        {\n            accessorKey: \"trainingDrillsCompleted\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training/drills completed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.P, {\n                    children: training.trainingTypes.nodes ? training.trainingTypes.nodes.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                item.title,\n                                \",\\xa0\"\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 35\n                        }, undefined)) : \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_trainingTypes_nodes_, _rowA_original_trainingTypes_nodes, _rowA_original_trainingTypes, _rowA_original, _rowB_original_trainingTypes_nodes_, _rowB_original_trainingTypes_nodes, _rowB_original_trainingTypes, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingTypes = _rowA_original.trainingTypes) === null || _rowA_original_trainingTypes === void 0 ? void 0 : (_rowA_original_trainingTypes_nodes = _rowA_original_trainingTypes.nodes) === null || _rowA_original_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_trainingTypes_nodes_ = _rowA_original_trainingTypes_nodes[0]) === null || _rowA_original_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_trainingTypes_nodes_.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingTypes = _rowB_original.trainingTypes) === null || _rowB_original_trainingTypes === void 0 ? void 0 : (_rowB_original_trainingTypes_nodes = _rowB_original_trainingTypes.nodes) === null || _rowB_original_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_trainingTypes_nodes_ = _rowB_original_trainingTypes_nodes[0]) === null || _rowB_original_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_trainingTypes_nodes_.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"where\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Where\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _training_vessel;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-nowrap\",\n                            children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || training.trainingLocationType || \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_14__.LocationModal, {\n                            vessel: training.vessel,\n                            iconClassName: \"size-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"trainer\",\n            cellAlignment: \"center\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Trainer\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 497,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                var _training_trainer_surname, _training_trainer_surname1;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-nowrap\",\n                    children: !isVesselView ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(training.trainer.firstName, training.trainer.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                children: [\n                                    training.trainer.firstName,\n                                    \" \",\n                                    (_training_trainer_surname = training.trainer.surname) !== null && _training_trainer_surname !== void 0 ? _training_trainer_surname : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(training.trainer.firstName, training.trainer.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                children: [\n                                    training.trainer.firstName,\n                                    \" \",\n                                    (_training_trainer_surname1 = training.trainer.surname) !== null && _training_trainer_surname1 !== void 0 ? _training_trainer_surname1 : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 503,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original_trainer, _rowA_original1, _rowA_original2, _rowA_original_trainer1, _rowA_original3, _rowB_original, _rowB_original_trainer, _rowB_original1, _rowB_original2, _rowB_original_trainer1, _rowB_original3;\n                const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.trainer) && (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainer = _rowA_original1.trainer) === null || _rowA_original_trainer === void 0 ? void 0 : _rowA_original_trainer.firstName), \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original2 = rowA.original) === null || _rowA_original2 === void 0 ? void 0 : _rowA_original2.trainer) && (rowA === null || rowA === void 0 ? void 0 : (_rowA_original3 = rowA.original) === null || _rowA_original3 === void 0 ? void 0 : (_rowA_original_trainer1 = _rowA_original3.trainer) === null || _rowA_original_trainer1 === void 0 ? void 0 : _rowA_original_trainer1.surname)) || \"\";\n                const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.trainer) && (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainer = _rowB_original1.trainer) === null || _rowB_original_trainer === void 0 ? void 0 : _rowB_original_trainer.firstName), \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original2 = rowB.original) === null || _rowB_original2 === void 0 ? void 0 : _rowB_original2.trainer) && (rowB === null || rowB === void 0 ? void 0 : (_rowB_original3 = rowB.original) === null || _rowB_original3 === void 0 ? void 0 : (_rowB_original_trainer1 = _rowB_original3.trainer) === null || _rowB_original_trainer1 === void 0 ? void 0 : _rowB_original_trainer1.surname)) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"who\",\n            cellAlignment: \"right\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Who\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 556,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full flex items-end gap-1\",\n                    children: training.members.nodes.map((member, index)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 45\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 41\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 41\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 37\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: !overdueBoolean,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 606,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 605,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 604,\n                columnNumber: 13\n            }, undefined),\n            excludeFilters.includes(\"overdueToggle\") ? isUnifiedDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 616,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OverdueTrainingList, {\n                trainingSessionDues: getUnifiedTrainingData(),\n                hideCrewColumn: true,\n                pageSize: 20\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 621,\n                columnNumber: 21\n            }, undefined) : overdueBoolean ? trainingSessionDuesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Loading overdue training...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 629,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OverdueTrainingList, {\n                trainingSessionDues: trainingSessionDues\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 634,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (trainingList === null || trainingList === void 0 ? void 0 : trainingList.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n                    columns: columns,\n                    data: trainingList,\n                    pageSize: 20,\n                    onChange: handleFilterChange,\n                    showToolbar: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 641,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"group border-b hover: \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 col-span-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"!w-[75px] h-auto\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    viewBox: \"0 0 147 147.01\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                            fill: \"#052350\",\n                                            fillRule: \"evenodd\",\n                                            opacity: \".97\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                            fill: \"#024450\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                            fill: \"#052451\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"  \",\n                                    children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 649,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false),\n            !excludeFilters.includes(\"overdueToggle\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full\",\n                onClick: ()=>toggleOverdueWrapper((prev)=>!prev),\n                children: overdueBoolean ? \"View all completed trainings\" : \"View overdue trainings\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 746,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 603,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"MHQV5rmcFGZBtb2m8YD7JtdoAFo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_18__.useQueryState,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_19__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, isVesselView = false, hideCrewColumn = false, pageSize = 20 } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_19__.useMediaQuery)(\"(min-width: 720px)\");\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Training\",\n            cell: (param)=>{\n                let { row } = param;\n                const data = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: data,\n                    type: \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 774,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            cellAlignment: \"left\",\n            header: \"Vessel\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_vessel;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isVesselView == false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                        children: ((_due_vessel = due.vessel) === null || _due_vessel === void 0 ? void 0 : _due_vessel.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"crew\",\n            cellAlignment: \"right\",\n            header: \"Crew\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status;\n                const due = row.original;\n                const members = due.members || [];\n                return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: members.map((member)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: due.status.isOverdue ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 830,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 813,\n                            columnNumber: 33\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"!rounded-full size-10\", (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.class),\n                    children: members.length\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 839,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            cellAlignment: \"right\",\n            header: \"Status\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status, _due_status1, _due_status2;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_17__.StatusBadge, {\n                    isOverdue: (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue,\n                    isUpcoming: (_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.dueWithinSevenDays,\n                    label: ((_due_status2 = due.status) === null || _due_status2 === void 0 ? void 0 : _due_status2.label) || \"Unknown Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 866,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"crew\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n            columns: columns,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 884,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"!w-[75px] h-auto\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            viewBox: \"0 0 147 147.01\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                    fill: \"#ffffff\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                    fill: \"#052350\",\n                                    fillRule: \"evenodd\",\n                                    opacity: \".97\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                    fill: \"#024450\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                    fill: \"#052451\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                    fill: \"#052250\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                    fill: \"#052350\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                    fill: \"#052250\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                    fill: \"#052350\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 950,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                    fill: \"#ffffff\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 970,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 894,\n                            columnNumber: 29\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"  \",\n                            children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 976,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 893,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 892,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 891,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s1(OverdueTrainingList, \"mDuYwCIVI9QBRqOzz3Dco716Kgk=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_19__.useMediaQuery\n    ];\n});\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});