"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-table.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingTable: function() { return /* binding */ UnifiedTrainingTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst getRowStatus = (rowData)=>{\n    if (rowData.status.isOverdue) {\n        return \"overdue\";\n    }\n    if (rowData.status.dueWithinSevenDays) {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\n// Status-based color classes for training titles\nconst getStatusColorClasses = (training)=>{\n    if (training.status.isOverdue) {\n        return \"text-destructive/80 hover:text-destructive\";\n    }\n    if (training.status.dueWithinSevenDays) {\n        return \"text-warning/80 hover:text-warning\";\n    }\n    return \"hover:text-curious-blue-400\";\n};\nconst UnifiedMobileTrainingCard = (param)=>{\n    let { data } = param;\n    var _data_trainingType, _data_originalData_trainingTypes, _data_originalData, _data_vessel, _data_originalData1, _data_originalData2;\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const isCompleted = data.category === \"completed\";\n    const isOverdue = data.category === \"overdue\";\n    const members = data.members || [];\n    const trainingTitle = ((_data_trainingType = data.trainingType) === null || _data_trainingType === void 0 ? void 0 : _data_trainingType.title) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full flex flex-col space-y-3 tablet-md:border-none border-b border-border py-3 small:pe-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap justify-between items-center\",\n                children: [\n                    isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/crew-training/info?id=\".concat(data.id),\n                        className: \"font-semibold text-base hover:text-primary\",\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-semibold text-base\", getStatusColorClasses(data)),\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 21\n                    }, undefined),\n                    !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.StatusBadge, {\n                            isOverdue: data.status.isOverdue,\n                            isUpcoming: data.status.dueWithinSevenDays,\n                            label: data.status.label || data.category\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 86,\n                columnNumber: 13\n            }, undefined),\n            !bp[\"tablet-md\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-[7px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Training/drill:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm\",\n                        children: isCompleted ? ((_data_originalData = data.originalData) === null || _data_originalData === void 0 ? void 0 : (_data_originalData_trainingTypes = _data_originalData.trainingTypes) === null || _data_originalData_trainingTypes === void 0 ? void 0 : _data_originalData_trainingTypes.nodes) ? data.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : trainingTitle : trainingTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 117,\n                columnNumber: 17\n            }, undefined),\n            !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Location:\" : \"Vessel:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_data_vessel = data.vessel) === null || _data_vessel === void 0 ? void 0 : _data_vessel.title) || ((_data_originalData1 = data.originalData) === null || _data_originalData1 === void 0 ? void 0 : _data_originalData1.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: data.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 135,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-lg\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                position: \"left\",\n                className: \"text-sm text-muted-foreground\",\n                label: isCompleted ? \"Team:\" : \"Crew Members:\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 flex-wrap\",\n                    children: [\n                        members.slice(0, bp[\"tablet-md\"] ? 8 : 6).map((member)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: !isCompleted && isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 33\n                            }, undefined);\n                        }),\n                        members.length > (bp[\"tablet-md\"] ? 8 : 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                                    className: \"w-fit\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-fit h-8\",\n                                        children: [\n                                            \"+\",\n                                            members.length - (bp[\"tablet-md\"] ? 8 : 6),\n                                            \" \",\n                                            \"more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                                    className: \"w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 max-h-64 overflow-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: members.slice(bp[\"tablet-md\"] ? 8 : 6).map((remainingMember)=>/*#__PURE__*/ {\n                                                var _remainingMember_firstName, _remainingMember_surname;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                            size: \"xs\",\n                                                            variant: \"secondary\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                className: \"text-xs\",\n                                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(remainingMember.firstName, remainingMember.surname)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 61\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 57\n                                                        }, undefined),\n                                                        \"\".concat((_remainingMember_firstName = remainingMember.firstName) !== null && _remainingMember_firstName !== void 0 ? _remainingMember_firstName : \"\", \" \").concat((_remainingMember_surname = remainingMember.surname) !== null && _remainingMember_surname !== void 0 ? _remainingMember_surname : \"\")\n                                                    ]\n                                                }, remainingMember.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 53\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 155,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-md\"] && isCompleted && ((_data_originalData2 = data.originalData) === null || _data_originalData2 === void 0 ? void 0 : _data_originalData2.trainer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center pt-2 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Trainer:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                size: \"sm\",\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                    className: \"text-sm\",\n                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(data.originalData.trainer.firstName, data.originalData.trainer.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    data.originalData.trainer.firstName,\n                                    \" \",\n                                    data.originalData.trainer.surname\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 232,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-md\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n                        className: \"my-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Category: \",\n                                    data.category\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 25\n                            }, undefined),\n                            data.dueDate && (()=>{\n                                const today = new Date();\n                                const dueDate = new Date(data.dueDate);\n                                const daysDifference = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: daysDifference > 0 ? \"Due in \".concat(daysDifference, \" days\") : daysDifference === 0 ? \"Due today\" : \"\".concat(Math.abs(daysDifference), \" days overdue\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 37\n                                }, undefined);\n                            })()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 84,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedMobileTrainingCard, \"ZxSHrfPd9jdclp97BkZrjvTJck4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = UnifiedMobileTrainingCard;\nconst UnifiedTrainingTable = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], unifiedData: preFilteredData, getVesselWithIcon, includeCompleted = true, memberId, isVesselView = false, showToolbar = false, pageSize } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery)(\"(min-width: 720px)\");\n    // Use pre-filtered data if available, otherwise merge and sort data\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (preFilteredData && Array.isArray(preFilteredData)) {\n            return preFilteredData;\n        }\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        preFilteredData,\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Determine if we have mixed data types or primarily one type\n    const hasOverdueOrUpcoming = unifiedData.some((item)=>item.category === \"overdue\" || item.category === \"upcoming\");\n    const hasCompleted = unifiedData.some((item)=>item.category === \"completed\");\n    // Create unified column structure for all training data types\n    const getUnifiedColumns = ()=>{\n        return [\n            // Mobile column - shows training card on mobile, adapts header based on data\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                        data: training\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 28\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    // Sort by category priority first, then by date\n                    const trainingA = rowA.original;\n                    const trainingB = rowB.original;\n                    const priorityA = trainingA.category === \"overdue\" ? 1 : trainingA.category === \"upcoming\" ? 2 : 3;\n                    const priorityB = trainingB.category === \"overdue\" ? 1 : trainingB.category === \"upcoming\" ? 2 : 3;\n                    if (priorityA !== priorityB) {\n                        return priorityA - priorityB;\n                    }\n                    const dateA = new Date(trainingA.dueDate).getTime();\n                    const dateB = new Date(trainingB.dueDate).getTime();\n                    return trainingA.category === \"completed\" ? dateB - dateA : dateA - dateB;\n                }\n            },\n            // Training Type column - shows training types for all data types\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training/drill\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_trainingTypes, _training_originalData, _training_trainingType, _training_trainingType1;\n                    const training = row.original;\n                    const isCompleted = training.category === \"completed\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                        children: isCompleted ? ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_trainingTypes = _training_originalData.trainingTypes) === null || _training_originalData_trainingTypes === void 0 ? void 0 : _training_originalData_trainingTypes.nodes) ? training.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\" : ((_training_trainingType1 = training.trainingType) === null || _training_trainingType1 === void 0 ? void 0 : _training_trainingType1.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainingTypes_nodes_, _rowA_original_originalData_trainingTypes_nodes, _rowA_original_originalData_trainingTypes, _rowA_original_originalData, _rowA_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_originalData_trainingTypes_nodes_, _rowB_original_originalData_trainingTypes_nodes, _rowB_original_originalData_trainingTypes, _rowB_original_originalData, _rowB_original, _rowB_original_trainingType, _rowB_original1;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes = _rowA_original_originalData.trainingTypes) === null || _rowA_original_originalData_trainingTypes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes = _rowA_original_originalData_trainingTypes.nodes) === null || _rowA_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes_ = _rowA_original_originalData_trainingTypes_nodes[0]) === null || _rowA_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_originalData_trainingTypes_nodes_.title) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes = _rowB_original_originalData.trainingTypes) === null || _rowB_original_originalData_trainingTypes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes = _rowB_original_originalData_trainingTypes.nodes) === null || _rowB_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes_ = _rowB_original_originalData_trainingTypes_nodes[0]) === null || _rowB_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_originalData_trainingTypes_nodes_.title) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Vessel column - shows vessel information for all data types\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: isVesselView ? \"\" : \"Where\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel, _training_originalData;\n                    const training = row.original;\n                    if (isVesselView) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 32\n                        }, undefined);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Crew column - shows crew members for all data types\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Who\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-lg\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_members, _training_originalData, _training_status;\n                    const training = row.original;\n                    const members = ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_members = _training_originalData.members) === null || _training_originalData_members === void 0 ? void 0 : _training_originalData_members.nodes) || training.members || [];\n                    return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: members.map((member, index)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: training.status.isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id || index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 33\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"!rounded-full size-10 flex items-center justify-center text-sm font-medium\", (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.class),\n                        children: members.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_members, _rowA_original_originalData, _rowA_original, _rowA_original1, _rowB_original_originalData_members, _rowB_original_originalData, _rowB_original, _rowB_original1, _membersA_, _membersA_1, _membersB_, _membersB_1;\n                    const membersA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_members = _rowA_original_originalData.members) === null || _rowA_original_originalData_members === void 0 ? void 0 : _rowA_original_originalData_members.nodes) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.members) || [];\n                    const membersB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_members = _rowB_original_originalData.members) === null || _rowB_original_originalData_members === void 0 ? void 0 : _rowB_original_originalData_members.nodes) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.members) || [];\n                    var _membersA__firstName, _membersA__surname;\n                    const valueA = \"\".concat((_membersA__firstName = membersA === null || membersA === void 0 ? void 0 : (_membersA_ = membersA[0]) === null || _membersA_ === void 0 ? void 0 : _membersA_.firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA === null || membersA === void 0 ? void 0 : (_membersA_1 = membersA[0]) === null || _membersA_1 === void 0 ? void 0 : _membersA_1.surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") || \"\";\n                    var _membersB__firstName, _membersB__surname;\n                    const valueB = \"\".concat((_membersB__firstName = membersB === null || membersB === void 0 ? void 0 : (_membersB_ = membersB[0]) === null || _membersB_ === void 0 ? void 0 : _membersB_.firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB === null || membersB === void 0 ? void 0 : (_membersB_1 = membersB[0]) === null || _membersB_1 === void 0 ? void 0 : _membersB_1.surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Trainer column - shows trainer for completed training, dash for others\n            {\n                accessorKey: \"trainer\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Trainer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData;\n                    const training = row.original;\n                    const trainer = (_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainer;\n                    if (!trainer || training.category !== \"completed\") {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground\",\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    var _trainer_surname;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-nowrap\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                    children: [\n                                        trainer.firstName,\n                                        \" \",\n                                        (_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainer, _rowA_original_originalData, _rowA_original, _rowA_original_originalData_trainer1, _rowA_original_originalData1, _rowA_original1, _rowB_original_originalData_trainer, _rowB_original_originalData, _rowB_original, _rowB_original_originalData_trainer1, _rowB_original_originalData1, _rowB_original1;\n                    const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainer = _rowA_original_originalData.trainer) === null || _rowA_original_originalData_trainer === void 0 ? void 0 : _rowA_original_originalData_trainer.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_originalData1 = _rowA_original1.originalData) === null || _rowA_original_originalData1 === void 0 ? void 0 : (_rowA_original_originalData_trainer1 = _rowA_original_originalData1.trainer) === null || _rowA_original_originalData_trainer1 === void 0 ? void 0 : _rowA_original_originalData_trainer1.surname) || \"\") || \"\";\n                    const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainer = _rowB_original_originalData.trainer) === null || _rowB_original_originalData_trainer === void 0 ? void 0 : _rowB_original_originalData_trainer.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_originalData1 = _rowB_original1.originalData) === null || _rowB_original_originalData1 === void 0 ? void 0 : (_rowB_original_originalData_trainer1 = _rowB_original_originalData1.trainer) === null || _rowB_original_originalData_trainer1 === void 0 ? void 0 : _rowB_original_originalData_trainer1.surname) || \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ];\n    };\n    // Create table columns\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)(getUnifiedColumns()), [\n        hasOverdueOrUpcoming,\n        hasCompleted,\n        isVesselView,\n        isWide\n    ]);\n    if (!(unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: \"No training data available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n            lineNumber: 596,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.FilteredTable, {\n        columns: columns,\n        data: unifiedData,\n        showToolbar: showToolbar,\n        rowStatus: getRowStatus,\n        pageSize: pageSize || 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 603,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(UnifiedTrainingTable, \"x7VOLZGRwr6UzHBlctBEB/GNMsI=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery\n    ];\n});\n_c1 = UnifiedTrainingTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingTable);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnifiedMobileTrainingCard\");\n$RefreshReg$(_c1, \"UnifiedTrainingTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\n"));

/***/ })

});