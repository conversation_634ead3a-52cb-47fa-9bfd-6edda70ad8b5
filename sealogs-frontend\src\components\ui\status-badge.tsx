import { cn } from '@/app/lib/utils'

// Reusable StatusBadge component following maintenance list pattern
// This component provides consistent status badge styling across the application
interface StatusBadgeProps {
    isOverdue?: boolean
    isUpcoming?: boolean
    label: string
    className?: string
}

export const StatusBadge = ({
    isOverdue,
    isUpcoming,
    label,
    className,
}: StatusBadgeProps) => {
    // Use the same pattern as maintenance StatusBadge - alert class for overdue, plain text for others
    if (isOverdue) {
        return (
            <span
                className={cn(
                    'alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1',
                    className,
                )}>
                {label}
            </span>
        )
    }

    // For upcoming/warning items, use a warning style similar to maintenance pattern
    if (isUpcoming) {
        return (
            <span
                className={cn(
                    'bg-warning/10 text-warning border border-warning/20 w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1',
                    className,
                )}>
                {label}
            </span>
        )
    }

    // Normal status - plain text like maintenance
    return (
        <span className={cn('text-nowrap text-sm xs:text-base', className)}>
            {label}
        </span>
    )
}
